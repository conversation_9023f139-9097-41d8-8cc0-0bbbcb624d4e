import React, { useState, useEffect, useRef } from 'react';
import { useLanguage } from '../LanguageContext.jsx';
import { useAppState } from '../contexts/AppStateContext.jsx';
import { useLocation } from 'react-router-dom';

export default function SalesManagement() {
  const { t, currentLanguage } = useLanguage();
  const location = useLocation();
  const {
    savedInvoices,
    setSavedInvoices,
    customers,
    currentUser,
    toasts,
    setToasts,
    products,
    setProducts,
    storeSettings
  } = useAppState();

  // Local state for sales management
  const [salesInvoiceFilter, setSalesInvoiceFilter] = useState('');
  const [selectedSalesInvoices, setSelectedSalesInvoices] = useState([]);
  const [showEditInvoiceModal, setShowEditInvoiceModal] = useState(false);
  const [showReturnModal, setShowReturnModal] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState(null);

  // New Sales Invoice Modal States
  const [showSalesModal, setShowSalesModal] = useState(false);
  const [salesInvoice, setSalesInvoice] = useState({
    invoiceNumber: '',
    date: new Date().toISOString().split('T')[0],
    customerId: 'GUEST',
    customerName: t('walkInCustomer', 'زبون عابر'),
    paymentMethod: t('cash', 'نقداً'),
    items: [],
    total: 0,
    discount: 0,
    tax: 0,
    finalTotal: 0
  });

  // Product selection states
  const [selectedProduct, setSelectedProduct] = useState('');
  const [productQuantity, setProductQuantity] = useState(1);
  const [productPrice, setProductPrice] = useState(0);
  const [productSearchTerm, setProductSearchTerm] = useState('');

  // Scanner states
  const [salesScannerInput, setSalesScannerInput] = useState('');
  const salesScannerRef = useRef(null);

  // Check for selected product from dashboard navigation
  useEffect(() => {
    if (location.state?.selectedProduct) {
      const product = location.state.selectedProduct;
      console.log('🛒 Sales: Received product from dashboard:', product.name);

      // Open sales modal with the selected product
      openSalesModal();

      // Add product to invoice after modal opens
      setTimeout(() => {
        addProductDirectlyToInvoice(product, 1);
      }, 200);

      // Clear the location state
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Auto-focus sales scanner when modal opens
  useEffect(() => {
    if (showSalesModal && salesScannerRef.current) {
      setTimeout(() => {
        salesScannerRef.current.focus();
      }, 100);
    }
  }, [showSalesModal]);

  // Format price function
  const formatPrice = (price) => {
    const amount = Math.round(price || 0).toString();
    const currency = currentLanguage === 'ar' ? 'د.ج' : 'DZD';

    if (currentLanguage === 'ar') {
      return `${currency} ${amount}`;
    } else {
      return `${amount} ${currency}`;
    }
  };

  // Toast notification function
  const showToast = (message, type = 'success', duration = 3000) => {
    const id = Date.now() + Math.random();
    const newToast = { id, message, type, duration };
    setToasts(prev => [...prev, newToast]);

    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, duration);
  };

  // Translate customer name based on language
  const translateCustomerName = (customerName) => {
    if (!customerName) {
      return currentLanguage === 'ar' ? 'زبون عابر' :
             currentLanguage === 'fr' ? 'Client de passage' :
             'Walk-in Customer';
    }
    return customerName;
  };

  // Filter invoices based on search
  const filteredSalesInvoices = savedInvoices.filter(invoice => {
    if (!salesInvoiceFilter.trim()) return true;

    const searchTerm = salesInvoiceFilter.toLowerCase();
    return (
      invoice.invoiceNumber?.toLowerCase().includes(searchTerm) ||
      invoice.customerName?.toLowerCase().includes(searchTerm) ||
      invoice.date?.toLowerCase().includes(searchTerm)
    );
  });

  // Toggle select all invoices
  const toggleSelectAll = (type, items) => {
    if (type === 'sales') {
      if (selectedSalesInvoices.length === items.length) {
        setSelectedSalesInvoices([]);
      } else {
        setSelectedSalesInvoices(items.map(item => item.id));
      }
    }
  };

  // Toggle select individual invoice
  const toggleSelectItem = (type, itemId) => {
    if (type === 'sales') {
      setSelectedSalesInvoices(prev =>
        prev.includes(itemId)
          ? prev.filter(id => id !== itemId)
          : [...prev, itemId]
      );
    }
  };

  // Delete selected invoices
  const deleteSelectedItems = (type) => {
    if (type === 'sales') {
      if (window.confirm(t('confirmDeleteSelectedInvoices', 'هل أنت متأكد من حذف الفواتير المحددة؟'))) {
        const updatedInvoices = savedInvoices.filter(inv => !selectedSalesInvoices.includes(inv.id));
        setSavedInvoices(updatedInvoices);
        setSelectedSalesInvoices([]);
        showToast(`🗑️ ${t('selectedInvoicesDeleted', 'تم حذف الفواتير المحددة')}`, 'success', 3000);
      }
    }
  };

  // View invoice details
  const viewInvoice = (invoice) => {
    // Implementation for viewing invoice details
    showToast(`👁️ ${t('viewingInvoice', 'عرض الفاتورة')} ${invoice.invoiceNumber}`, 'info', 2000);
  };

  // Print invoice
  const printInvoice = (invoice) => {
    // Implementation for printing invoice
    showToast(`🖨️ ${t('printingInvoice', 'طباعة الفاتورة')} ${invoice.invoiceNumber}`, 'success', 2000);
  };

  // Print thermal invoice
  const printThermalInvoice = (invoice) => {
    // Implementation for thermal printing
    showToast(`🧾 ${t('thermalPrintingInvoice', 'طباعة حرارية للفاتورة')} ${invoice.invoiceNumber}`, 'success', 2000);
  };

  // Open edit invoice modal
  const openEditInvoiceModal = (invoice) => {
    setEditingInvoice(invoice);
    setShowEditInvoiceModal(true);
  };

  // Open return modal
  const openReturnModal = (invoice) => {
    setEditingInvoice(invoice);
    setShowReturnModal(true);
  };

  // Delete invoice
  const deleteInvoice = (invoice) => {
    if (window.confirm(t('confirmDeleteInvoice', 'هل أنت متأكد من حذف هذه الفاتورة؟'))) {
      const updatedInvoices = savedInvoices.filter(inv => inv.id !== invoice.id);
      setSavedInvoices(updatedInvoices);
      showToast(`🗑️ ${t('invoiceDeleted', 'تم حذف الفاتورة')} ${invoice.invoiceNumber}`, 'success', 3000);
    }
  };

  // Print sales report
  const printSalesReport = () => {
    showToast(`📊 ${t('generatingSalesReport', 'إنشاء تقرير المبيعات')}`, 'info', 2000);
  };

  // New Sales Invoice Functions
  const openSalesModal = () => {
    console.log('🛒 Opening Sales Modal - Resetting all states');

    // Reset sales invoice state completely
    setSalesInvoice({
      invoiceNumber: 'INV-' + Date.now(),
      date: new Date().toISOString().split('T')[0],
      customerId: 'GUEST',
      customerName: t('walkInCustomer', 'زبون عابر'),
      paymentMethod: t('cash', 'نقداً'),
      items: [],
      total: 0,
      discount: 0,
      tax: 0,
      finalTotal: 0
    });

    // Clear product selection states
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
    setProductSearchTerm('');
    setSalesScannerInput('');

    console.log('🛒 All states cleared - Opening sales modal');
    setShowSalesModal(true);
  };

  const closeSalesModal = () => {
    console.log('🛒 Closing Sales Modal');

    // Check if there are items in the invoice
    if (salesInvoice.items.length > 0) {
      console.log('⚠️ Warning: Closing modal with items in invoice');
      showToast(`⚠️ ${t('invoiceHasItems', 'الفاتورة تحتوي على منتجات - استخدم زر الحفظ أو احذف المنتجات')}`, 'warning', 4000);
      return; // Prevent closing if there are items
    }

    setShowSalesModal(false);
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
    setProductSearchTerm('');
    setSalesScannerInput('');

    console.log('🛒 Sales Modal closed - All sales states cleared');
  };

  const forceCloseSalesModal = () => {
    console.log('🛒 Force Closing Sales Modal');
    setShowSalesModal(false);
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
    setProductSearchTerm('');
    setSalesScannerInput('');
    console.log('🛒 Sales Modal force closed');
  };

  // Sanitize scanned code
  const sanitizeScannedCode = (code) => {
    if (!code) return '';
    return code.toString().trim().replace(/[^\w\d-]/g, '');
  };

  // Validate scanned code
  const isValidScannedCode = (code) => {
    if (!code || typeof code !== 'string') return false;
    const sanitized = sanitizeScannedCode(code);
    return sanitized.length >= 3 && sanitized.length <= 50;
  };

  // Sales scanner input handler
  const handleSalesScannerInput = (e) => {
    if (!showSalesModal) {
      console.log('🚫 Sales scanner BLOCKED - sales modal not open');
      return;
    }

    const rawInput = e.target.value;
    const cleanedCode = sanitizeScannedCode(rawInput);
    setSalesScannerInput(cleanedCode);

    // Auto-add product when valid barcode is detected
    if (cleanedCode.length >= 3) {
      clearTimeout(window.salesScannerValidationTimeout);
      window.salesScannerValidationTimeout = setTimeout(() => {
        const foundProduct = products.find(p => p.barcode === cleanedCode && cleanedCode.trim() !== '');
        if (foundProduct) {
          console.log('🛒 Sales scanner: Auto-detected product:', foundProduct.name);
          addProductDirectlyToInvoice(foundProduct, 1);
          setSalesScannerInput(''); // Clear after adding
        }
      }, 500);
    }
  };

  // Sales scanner key press handler
  const handleSalesScannerKeyPress = (e) => {
    if (!showSalesModal) {
      console.log('🚫 Sales scanner keypress BLOCKED - sales modal not open');
      return;
    }

    if (e.key === 'Enter' && e.type === 'keydown') {
      e.preventDefault();
      e.stopPropagation();
      const scannedCode = e.target.value.trim();
      console.log('🛒 Sales Enter pressed with code:', scannedCode);

      // Clear the input on Enter - product is already added automatically
      setSalesScannerInput('');
    }
  };

  // Filter products for sales modal
  const filteredProductsForSales = products.filter(product => {
    if (!productSearchTerm) return true;
    const searchLower = productSearchTerm.toLowerCase();
    return product.name.toLowerCase().includes(searchLower) ||
           product.id.toLowerCase().includes(searchLower) ||
           (product.barcode && product.barcode.toLowerCase().includes(searchLower));
  });

  // Add product to invoice - ORIGINAL METHOD FROM APP_OLD.JSX
  const addProductToInvoice = () => {
    // Basic validation
    if (!selectedProduct) {
      showToast(`⚠️ ${t('pleaseSelectProduct', 'يرجى اختيار منتج')}`, 'warning', 3000);
      return;
    }
    if (productQuantity <= 0) {
      showToast(`⚠️ ${t('pleaseEnterValidQuantity', 'يرجى إدخال كمية صحيحة')}`, 'warning', 3000);
      return;
    }

    // Find product
    const product = products.find(p => p.id === selectedProduct);
    if (!product) {
      showToast(`❌ ${t('productNotFoundInSystem', 'المنتج غير موجود')}`, 'error', 3000);
      return;
    }

    // Check stock
    if (productQuantity > product.stock) {
      showToast(`❌ ${t('quantityExceedsStock', 'الكمية المطلوبة أكبر من المتوفر')}: (${productQuantity}) > (${product.stock})`, 'error', 3000);
      return;
    }

    // Create new item
    const newItem = {
      productId: product.id,
      productName: product.name,
      name: product.name,
      price: productPrice || product.sellPrice || product.price || 0,
      quantity: productQuantity,
      total: (productPrice || product.sellPrice || product.price || 0) * productQuantity
    };

    // Add to invoice - DIRECT UPDATE LIKE ORIGINAL
    const newItems = [...salesInvoice.items, newItem];
    const total = newItems.reduce((sum, item) => sum + item.total, 0);
    const tax = total * (storeSettings.taxRate / 100);
    const finalTotal = total + tax - salesInvoice.discount;

    setSalesInvoice({
      ...salesInvoice,
      items: newItems,
      total,
      tax,
      finalTotal
    });

    // Clear form
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
    setProductSearchTerm('');

    showToast(`✅ ${t('productAddedToInvoice', 'تم إضافة')} ${product.name} ${t('toInvoice', 'للفاتورة')}`, 'success', 2000);
  };

  // Add product directly from barcode scan - ORIGINAL METHOD FROM APP_OLD.JSX
  const addProductDirectlyToInvoice = (product, quantity = 1) => {
    if (!product) return;

    if (product.stock < quantity) {
      showToast(`❌ ${t('insufficientStock', 'المخزون غير كافي')} (${t('available', 'متوفر')}: ${product.stock})`, 'error', 3000);
      return;
    }

    // Check if product already exists in invoice
    const existingItemIndex = salesInvoice.items.findIndex(item => item.productId === product.id);

    let newItems;
    if (existingItemIndex >= 0) {
      // Update existing item
      newItems = [...salesInvoice.items];
      const newQuantity = newItems[existingItemIndex].quantity + quantity;

      if (newQuantity > product.stock) {
        showToast(`❌ ${t('totalQuantityExceedsStock', 'الكمية الإجمالية تتجاوز المخزون')} (${t('available', 'متوفر')}: ${product.stock})`, 'error', 3000);
        return;
      }

      newItems[existingItemIndex].quantity = newQuantity;
      newItems[existingItemIndex].total = newQuantity * (product.sellPrice || product.price);
    } else {
      // Add new item
      const newItem = {
        productId: product.id,
        productName: product.name,
        name: product.name,
        quantity: quantity,
        price: product.sellPrice || product.price,
        total: quantity * (product.sellPrice || product.price)
      };
      newItems = [...salesInvoice.items, newItem];
    }

    // DIRECT UPDATE LIKE ORIGINAL - NO SEPARATE FUNCTION
    const total = newItems.reduce((sum, item) => sum + item.total, 0);
    const tax = total * (storeSettings.taxRate / 100);
    const finalTotal = total + tax - salesInvoice.discount;

    setSalesInvoice({
      ...salesInvoice,
      items: newItems,
      total,
      tax,
      finalTotal
    });

    showToast(`✅ ${t('productAddedToInvoice', 'تم إضافة')} ${product.name} ${t('toInvoice', 'للفاتورة')}`, 'success', 2000);
  };

  // Remove product from invoice - ORIGINAL METHOD FROM APP_OLD.JSX
  const removeProductFromInvoice = (productId) => {
    const newItems = salesInvoice.items.filter(item => item.productId !== productId);

    // DIRECT UPDATE LIKE ORIGINAL - NO SEPARATE FUNCTION
    const total = newItems.reduce((sum, item) => sum + item.total, 0);
    const tax = total * (storeSettings.taxRate / 100);
    const finalTotal = total + tax - salesInvoice.discount;

    setSalesInvoice({
      ...salesInvoice,
      items: newItems,
      total,
      tax,
      finalTotal
    });

    showToast(`🗑️ ${t('productRemovedFromInvoice', 'تم حذف المنتج من الفاتورة')}`, 'info', 2000);
  };



  // Save sales invoice
  const saveSalesInvoice = () => {
    if (salesInvoice.items.length === 0) {
      showToast(`⚠️ ${t('pleaseAddProductsToInvoice', 'يرجى إضافة منتجات للفاتورة')}`, 'warning', 3000);
      return;
    }

    // Validate credit payment for walk-in customers
    if (salesInvoice.customerId === 'GUEST' && salesInvoice.paymentMethod === 'دين') {
      showToast(`❌ ${t('walkInCustomerCannotHaveCredit', 'الزبون العابر لا يمكن أن يكون له دين')}`, 'error', 3000);
      return;
    }

    // Update product stock
    const updatedProducts = products.map(product => {
      const invoiceItem = salesInvoice.items.find(item => item.productId === product.id);
      if (invoiceItem) {
        return {
          ...product,
          stock: product.stock - invoiceItem.quantity
        };
      }
      return product;
    });

    // Create new invoice
    const newInvoice = {
      ...salesInvoice,
      id: 'INV-' + Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Save to state
    setProducts(updatedProducts);
    setSavedInvoices([newInvoice, ...savedInvoices]);

    // Save to localStorage
    localStorage.setItem('icaldz-products', JSON.stringify(updatedProducts));
    localStorage.setItem('icaldz-invoices', JSON.stringify([newInvoice, ...savedInvoices]));

    showToast(`✅ ${t('invoiceSaved', 'تم حفظ الفاتورة')} ${newInvoice.invoiceNumber}`, 'success', 3000);

    // Reset and close modal
    forceCloseSalesModal();
  };

  return (
    <div className={`sales-page-modern lang-${currentLanguage}`}>
      {/* Ultra Modern Header */}
      <div className={`ultra-modern-header ${currentLanguage === 'ar' ? 'rtl' : 'ltr'}`}>
        <div className="header-content">
          <div className="header-left">
            <div className="page-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 7H17L19 2H5L7 7ZM7 7L5.5 13.5C5.33333 14.1667 5.5 15 6.5 15H17.5C18.5 15 18.6667 14.1667 18.5 13.5L17 7M7 7H17M9 19.5C9.82843 19.5 10.5 18.8284 10.5 18C10.5 17.1716 9.82843 16.5 9 16.5C8.17157 16.5 7.5 17.1716 7.5 18C7.5 18.8284 8.17157 19.5 9 19.5ZM20 19.5C20.8284 19.5 21.5 18.8284 21.5 18C21.5 17.1716 20.8284 16.5 20 16.5C19.1716 16.5 18.5 17.1716 18.5 18C18.5 18.8284 19.1716 19.5 20 19.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className="header-text">
              <h1>{t('salesManagement', 'إدارة المبيعات')}</h1>
              <p>{t('manageSalesAndInvoices', 'إدارة المبيعات والفواتير')}</p>
            </div>
          </div>
          <div className="header-actions">
            <button
              className="ultra-modern-btn success"
              onClick={openSalesModal}
              title={t('newSalesInvoice', 'فاتورة مبيعات جديدة')}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {t('newSalesInvoice', 'فاتورة مبيعات جديدة')}
            </button>
            <button
              className="ultra-modern-btn primary"
              onClick={printSalesReport}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2ZM14 2V8H20M16 13H8M16 17H8M10 9H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {t('report', 'تقرير')}
            </button>
          </div>
        </div>
      </div>

      {/* Ultra Modern Statistics Grid */}
      <div className={`ultra-modern-stats ${currentLanguage === 'ar' ? 'rtl' : 'ltr'}`}>
        <div className="stat-card success">
          <div className="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2V22M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6313 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6313 13.6815 18 14.5717 18 15.5C18 16.4283 17.6313 17.3185 16.9749 17.9749C16.3185 18.6313 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className="stat-content">
            <div className="stat-number">{formatPrice(savedInvoices.reduce((sum, inv) => sum + (inv.finalTotal || 0), 0))}</div>
            <div className="stat-label">{t('totalSales', 'إجمالي المبيعات')}</div>
          </div>
        </div>
        <div className="stat-card primary">
          <div className="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2ZM14 2V8H20M16 13H8M16 17H8M10 9H8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className="stat-content">
            <div className="stat-number">{savedInvoices.length}</div>
            <div className="stat-label">{t('invoiceCount', 'عدد الفواتير')}</div>
          </div>
        </div>
        <div className="stat-card info">
          <div className="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className="stat-content">
            <div className="stat-number">
              {savedInvoices.length > 0
                ? formatPrice(savedInvoices.reduce((sum, inv) => sum + (inv.finalTotal || 0), 0) / savedInvoices.length)
                : formatPrice(0)
              }
            </div>
            <div className="stat-label">{t('averageInvoice', 'متوسط الفاتورة')}</div>
          </div>
        </div>
        <div className="stat-card warning">
          <div className="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 7H16V6C16 5.46957 15.7893 4.96086 15.4142 4.58579C15.0391 4.21071 14.5304 4 14 4H10C9.46957 4 8.96086 4.21071 8.58579 4.58579C8.21071 4.96086 8 5.46957 8 6V7H5C4.46957 7 3.96086 7.21071 3.58579 7.58579C3.21071 7.96086 3 8.46957 3 9V19C3 19.5304 3.21071 20.0391 3.58579 20.4142C3.96086 20.7893 4.46957 21 5 21H19C19.5304 21 20.0391 20.7893 20.4142 20.4142C20.7893 20.0391 21 19.5304 21 19V9C21 8.46957 20.7893 7.96086 20.4142 7.58579C20.0391 7.21071 19.5304 7 19 7ZM10 6H14V7H10V6Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className="stat-content">
            <div className="stat-number">{savedInvoices.filter(inv => inv.paymentMethod === 'دين' || inv.paymentMethod === 'Dette' || inv.paymentMethod === 'Credit').length}</div>
            <div className="stat-label">{t('creditInvoices', 'فواتير دين')}</div>
          </div>
        </div>
      </div>

      {/* Ultra Modern Search and Filters */}
      <div className={`ultra-modern-filters ${currentLanguage === 'ar' ? 'rtl' : 'ltr'}`}>
        <div className="search-container">
          <div className="search-input-wrapper">
            <svg className="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <input
              type="text"
              placeholder={t('searchSalesInvoices', 'البحث في فواتير المبيعات (رقم الفاتورة، اسم الزبون)...')}
              value={salesInvoiceFilter}
              onChange={(e) => setSalesInvoiceFilter(e.target.value)}
              className="ultra-modern-search"
            />
          </div>
        </div>
        <div className="action-buttons">
          {salesInvoiceFilter && (
            <button
              className="ultra-modern-btn ghost"
              onClick={() => setSalesInvoiceFilter('')}
              title={t('clearFilters', 'مسح الفلاتر')}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H21M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {t('clearFilters', 'مسح الفلاتر')}
            </button>
          )}
          {selectedSalesInvoices.length > 0 && (currentUser.role === 'مدير' || currentUser.role === 'admin') && (
            <button
              className="ultra-modern-btn danger"
              onClick={() => deleteSelectedItems('sales')}
              title={t('deleteSelected', 'حذف المحدد')}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H21M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19ZM10 11V17M14 11V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {t('deleteSelected', 'حذف المحدد')} ({selectedSalesInvoices.length})
            </button>
          )}
        </div>
      </div>

      {/* Ultra Modern Sales Table - Green Theme */}
      <div className="ultra-modern-table-section-sales">
        <div className={`table-header ${currentLanguage === 'ar' ? 'rtl' : 'ltr'}`}>
          <div className="table-title">
            <h2>{t('salesInvoices', 'فواتير المبيعات')} ({filteredSalesInvoices.length})</h2>
          </div>
          <div className="table-actions">
            {selectedSalesInvoices.length > 0 && (
              <div className="selection-info">
                <span className="selection-count">{selectedSalesInvoices.length} {t('selected', 'محدد')}</span>
              </div>
            )}
          </div>
        </div>

        <div className="ultra-modern-table-container">
          <table className="ultra-modern-table-sales">
            <thead>
              <tr>
                <th className="checkbox-column">
                  <label className="modern-checkbox">
                    <input
                      type="checkbox"
                      checked={filteredSalesInvoices.length > 0 && selectedSalesInvoices.length === filteredSalesInvoices.length}
                      onChange={() => toggleSelectAll('sales', filteredSalesInvoices)}
                      title={t('selectAll', 'تحديد الكل')}
                    />
                    <span className="checkmark"></span>
                  </label>
                </th>
                <th>{t('invoiceNumber', 'رقم الفاتورة')}</th>
                <th>{t('date', 'التاريخ')}</th>
                <th>{t('customer', 'الزبون')}</th>
                <th>{t('paymentMethod', 'طريقة الدفع')}</th>
                <th>{t('amount', 'المبلغ')}</th>
                <th>{t('status', 'الحالة')}</th>
                <th>{t('actions', 'الإجراءات')}</th>
              </tr>
            </thead>
            <tbody>
              {filteredSalesInvoices.length === 0 ? (
                <tr>
                  <td colSpan="8" className="empty-state">
                    <div className="empty-content">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2ZM14 2V8H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p>
                        {salesInvoiceFilter.trim()
                          ? `${t('noInvoicesMatchingFilter', 'لا توجد فواتير مطابقة للفلتر')}: "${salesInvoiceFilter}"`
                          : t('noInvoicesSaved', 'لا توجد فواتير محفوظة')
                        }
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredSalesInvoices.map((invoice) => (
                  <tr key={invoice.id} className="table-row">
                    <td className="checkbox-column">
                      <label className="modern-checkbox">
                        <input
                          type="checkbox"
                          checked={selectedSalesInvoices.includes(invoice.id)}
                          onChange={() => toggleSelectItem('sales', invoice.id)}
                        />
                        <span className="checkmark"></span>
                      </label>
                    </td>
                    <td className="invoice-number">#{invoice.invoiceNumber}</td>
                    <td className="invoice-date">{invoice.date}</td>
                    <td className="customer-name">{translateCustomerName(invoice.customerName)}</td>
                    <td className="payment-method">
                      <span className={`ultra-modern-badge ${(invoice.paymentMethod === 'نقداً' || invoice.paymentMethod === 'Espèces' || invoice.paymentMethod === 'En espèces' || invoice.paymentMethod === 'Cash') ? 'success' : 'warning'}`}>
                        {invoice.paymentMethod === 'نقداً' ? t('cash', 'نقداً') :
                         invoice.paymentMethod === 'دين' ? t('credit', 'دين') :
                         invoice.paymentMethod || t('cash', 'نقداً')}
                      </span>
                    </td>
                    <td className="amount">{formatPrice(invoice.finalTotal)}</td>
                    <td className="status">
                      <span className={`ultra-modern-badge ${(invoice.paymentMethod === 'نقداً' || invoice.paymentMethod === 'Espèces' || invoice.paymentMethod === 'En espèces' || invoice.paymentMethod === 'Cash') ? 'success' : 'warning'}`}>
                        {(invoice.paymentMethod === 'نقداً' || invoice.paymentMethod === 'Espèces' || invoice.paymentMethod === 'En espèces' || invoice.paymentMethod === 'Cash') ? t('paid', 'مدفوعة') : t('debt', 'دين')}
                      </span>
                    </td>
                    <td className="actions">
                      <div className="action-buttons-group">
                        <button
                          className="ultra-modern-btn-sales small outline"
                          onClick={() => viewInvoice(invoice)}
                          title={t('viewInvoiceDetails', 'عرض تفاصيل الفاتورة')}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                        <button
                          className="ultra-modern-btn-sales small ghost"
                          onClick={() => printInvoice(invoice)}
                          title={t('normalPrint', 'طباعة عادية')}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 9V2H18V9M6 18H4C3.46957 18 2.96086 17.7893 2.58579 17.4142C2.21071 17.0391 2 16.5304 2 16V11C2 10.4696 2.21071 9.96086 2.58579 9.58579C2.96086 9.21071 3.46957 9 4 9H20C20.5304 9 21.0391 9.21071 21.4142 9.58579C21.7893 9.96086 22 10.4696 22 11V16C22 16.5304 21.7893 17.0391 21.4142 17.4142C21.0391 17.7893 20.5304 18 20 18H18M6 14H18V22H6V14Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                        <button
                          className="ultra-modern-btn-sales small primary"
                          onClick={() => printThermalInvoice(invoice)}
                          title={t('thermalPrint', 'طباعة حرارية')}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2ZM14 2V8H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                        {(currentUser.role === 'مدير' || currentUser.role === 'admin') && (
                          <>
                            <button
                              className="ultra-modern-btn-sales small ghost"
                              onClick={() => openEditInvoiceModal(invoice)}
                              title={t('editInvoice', 'تعديل الفاتورة (المدير فقط)')}
                            >
                              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </button>
                            <button
                              className="ultra-modern-btn-sales small danger"
                              onClick={() => deleteInvoice(invoice)}
                              title={t('deleteInvoice', 'حذف الفاتورة (المدير فقط)')}
                            >
                              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 6H21M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              </svg>
                            </button>
                          </>
                        )}
                        <button
                          className="ultra-modern-btn-sales small outline"
                          onClick={() => openReturnModal(invoice)}
                          title={t('returnProducts', 'إرجاع منتجات')}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 10H21M7 3V10M17 14V21M17 21L13 17M17 21L21 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Clean Rapid Sales Interface */}
      {showSalesModal && (
        <div className="ultra-modern-modal-overlay" onClick={(e) => e.target === e.currentTarget && setShowSalesModal(false)}>
          <div className={`ultra-modern-rapid-sales ${currentLanguage === 'ar' ? 'rtl' : 'ltr'}`}>

            {/* Header */}
            <div className="rapid-sales-header">
              <div className="header-left">
                <div className="page-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 7H17L19 2H5L7 7ZM7 7L5.5 13.5C5.33333 14.1667 5.5 15 6.5 15H17.5C18.5 15 18.6667 14.1667 18.5 13.5L17 7M7 7H17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="header-text">
                  <h2>{t('newSalesInvoice', 'فاتورة مبيعات جديدة')}</h2>
                  <p>#{salesInvoice.invoiceNumber}</p>
                </div>
              </div>
              <button className="rapid-close-btn" onClick={() => setShowSalesModal(false)}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>

            {/* NEW BARCODE SCANNER + LCD IN SAME ROW - Restored from App_old.jsx */}
            <div className="sales-barcode-scanner-row">
              {/* Scanner Input Section */}
              <div className="scanner-input-row">
                <h3 className="scanner-title">📷 <span className="scanner-status-active">{t('active', 'نشط')}</span> - {t('scanBarcode', 'مسح الباركود')} 📷</h3>
                <div className="barcode-input-container">
                  <span className="barcode-icon">📷</span>
                  <input
                    type="text"
                    placeholder={t('scanToAddProduct', 'امسح الباركود لإضافة منتج')}
                    value={salesScannerInput}
                    onChange={handleSalesScannerInput}
                    onKeyDown={handleSalesScannerKeyPress}
                    onFocus={() => {
                      // 🔧 BARCODE SCANNER FIX: Disable shortcuts when barcode input is focused
                      if (window.barcodeShortcutManager) {
                        window.barcodeShortcutManager.isBarcodeActive = true;
                        window.barcodeShortcutManager.setShortcutsEnabled(false);
                        console.log('🔧 BARCODE FIX: Sales scanner focused - shortcuts disabled');
                      }
                    }}
                    onBlur={() => {
                      // 🔧 BARCODE SCANNER FIX: Re-enable shortcuts when barcode input loses focus
                      setTimeout(() => {
                        if (window.barcodeShortcutManager && !window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)) {
                          window.barcodeShortcutManager.isBarcodeActive = false;
                          window.barcodeShortcutManager.setShortcutsEnabled(true);
                          console.log('🔧 BARCODE FIX: Sales scanner blurred - shortcuts re-enabled');
                        }
                      }, 100);
                    }}
                    className="barcode-input"
                    ref={salesScannerRef}
                    autoFocus
                  />
                  <button
                    type="button"
                    className="btn btn-warning btn-sm"
                    onClick={() => {
                      setSalesScannerInput('');
                    }}
                    title={t('clearBarcode', 'مسح الباركود')}
                  >
                    🗑️ {t('clear', 'مسح')}
                  </button>
                </div>
              </div>

              {/* LCD Display Section - NOW SHOWS TOTAL FINAL */}
              <div className="lcd-display-row">
                <div className="lcd-screen-big">
                  <div className="total-final-display">
                    <div className="total-final-label">{t('finalTotal', 'المجموع النهائي')}:</div>
                    <div className="total-final-amount">{formatPrice(salesInvoice.finalTotal)}</div>
                    <div className="total-final-currency">DZD</div>
                  </div>
                </div>
              </div>
            </div>



            {/* Modern Side-by-Side Layout: Table + Payment Details */}
            <div className="modern-invoice-layout">
              {/* Left Side: Items Table */}
              <div className="items-section-modern">
                <div className="section-header">
                  <h3>🛍️ {t('invoiceItems', 'عناصر الفاتورة')}</h3>
                  <div className="items-count">
                    {salesInvoice.items.length} {t('items', 'عناصر')}
                  </div>
                </div>

                {/* Product Selection Controls in Table Header */}
                <div className="table-header-product-selection">
                  <div className="product-selection-row">
                    <div className="product-select-group-header">
                      <label className="select-label-header">{t('selectProduct', 'اختر منتج')}</label>
                      <select
                        className="modern-product-select-header dropdown-up"
                        value={selectedProduct}
                        onChange={(e) => {
                          setSelectedProduct(e.target.value);
                          const product = products.find(p => p.id === e.target.value);
                          if (product) {
                            setProductPrice(product.sellPrice);
                          }
                        }}
                      >
                        <option value="">{t('chooseProduct', 'اختر منتج...')}</option>
                        {products.filter(p => p.stock > 0).map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name} - {formatPrice(product.sellPrice)} ({product.stock} {t('inStock', 'في المخزون')})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="quantity-select-group-header">
                      <label className="select-label-header">{t('quantity', 'الكمية')}</label>
                      <div className="quantity-controls-header">
                        <button
                          type="button"
                          className="quantity-btn-header decrease"
                          onClick={() => setProductQuantity(Math.max(1, productQuantity - 1))}
                          disabled={productQuantity <= 1}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                        <input
                          type="number"
                          className="quantity-input-header"
                          min="1"
                          value={productQuantity}
                          onChange={(e) => setProductQuantity(parseInt(e.target.value) || 1)}
                        />
                        <button
                          type="button"
                          className="quantity-btn-header increase"
                          onClick={() => {
                            const product = products.find(p => p.id === selectedProduct);
                            if (product && productQuantity < product.stock) {
                              setProductQuantity(productQuantity + 1);
                            }
                          }}
                          disabled={!selectedProduct || productQuantity >= (products.find(p => p.id === selectedProduct)?.stock || 0)}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div className="add-button-group-header">
                      <button
                        className="modern-add-btn-header"
                        onClick={addProductToInvoice}
                        disabled={!selectedProduct}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <span>{t('addToInvoice', 'إضافة للفاتورة')}</span>
                      </button>
                    </div>
                  </div>
                </div>

                {salesInvoice.items.length === 0 ? (
                  <div className="no-items-modern">
                    <div className="empty-state">
                      <div className="empty-icon">📦</div>
                      <h4>{t('noProductsAdded', 'لم يتم إضافة أي منتجات بعد')}</h4>
                      <p>{t('scanOrSelectProducts', 'امسح الباركود أو اختر المنتجات من القائمة')}</p>
                    </div>
                  </div>
                ) : (
                  <div className="items-table-modern">
                    <table className={`modern-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`}>
                      <thead>
                        <tr>
                          <th>{t('productName', 'المنتج')}</th>
                          <th>{t('quantity', 'الكمية')}</th>
                          <th>{t('price', 'السعر')}</th>
                          <th>{t('total', 'المجموع')}</th>
                          <th>{t('action', 'إجراء')}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {salesInvoice.items.map((item, index) => (
                          <tr key={index} className="table-row-modern">
                            <td className="product-name-cell">
                              <div className="product-info">
                                <span className="product-name">{item.productName}</span>
                              </div>
                            </td>
                            <td className="quantity-cell">
                              <input
                                type="number"
                                value={item.quantity}
                                onChange={(e) => {
                                  const newQuantity = parseInt(e.target.value) || 1;
                                  const product = products.find(p => p.id === item.productId);
                                  if (product && newQuantity <= product.stock) {
                                    const newItems = [...salesInvoice.items];
                                    newItems[index].quantity = newQuantity;
                                    newItems[index].total = newQuantity * item.price;

                                    // DIRECT UPDATE LIKE ORIGINAL - NO SEPARATE FUNCTION
                                    const total = newItems.reduce((sum, item) => sum + item.total, 0);
                                    const tax = total * (storeSettings.taxRate / 100);
                                    const finalTotal = total + tax - salesInvoice.discount;

                                    setSalesInvoice({
                                      ...salesInvoice,
                                      items: newItems,
                                      total,
                                      tax,
                                      finalTotal
                                    });
                                  } else {
                                    showToast(`❌ ${t('quantityRequiredExceedsStock', 'الكمية المطلوبة أكبر من المتوفر')} (${product?.stock || 0})`, 'error', 3000);
                                  }
                                }}
                                min="1"
                                className="quantity-input-modern"
                              />
                            </td>
                            <td className="price-cell">{formatPrice(item.price)}</td>
                            <td className="total-cell">
                              <span className="total-amount">{formatPrice(item.total)}</span>
                            </td>
                            <td className="action-cell">
                              <button
                                className="btn-delete-modern"
                                onClick={() => removeProductFromInvoice(item.productId)}
                                title={t('deleteTitle', 'حذف')}
                              >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M3 6H5H21M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>

              {/* Right Side: Payment Details */}
              <div className="payment-details-modern">
                <div className="section-header">
                  <h3>💳 {t('paymentDetails', 'تفاصيل الدفع')}</h3>
                  <div className="payment-status">
                    <span className={`status-badge ${salesInvoice.paymentMethod === 'نقداً' ? 'cash' : 'credit'}`}>
                      {salesInvoice.paymentMethod}
                    </span>
                  </div>
                </div>

                <div className="payment-form-modern">
                  {/* Customer Selection */}
                  <div className="form-group-modern">
                    <label className="form-label">{t('customer', 'الزبون')}</label>
                    <select
                      className="form-select-modern"
                      value={salesInvoice.customerId}
                      onChange={(e) => {
                        const customerId = e.target.value;
                        if (customerId === 'GUEST') {
                          const total = salesInvoice.items.reduce((sum, item) => sum + item.total, 0);
                          const tax = total * (storeSettings.taxRate / 100);
                          const finalTotal = total + tax - 0;
                          setSalesInvoice({
                            ...salesInvoice,
                            customerId: 'GUEST',
                            customerName: t('walkInCustomer', 'زبون عابر'),
                            discount: 0,
                            total,
                            tax,
                            finalTotal
                          });
                        } else {
                          const customer = customers.find(c => c.id === customerId);
                          if (customer) {
                            const total = salesInvoice.items.reduce((sum, item) => sum + item.total, 0);
                            const totalProfitMargin = salesInvoice.items.reduce((sum, item) => {
                              const product = products.find(p => p.id === item.productId);
                              if (product && product.buyPrice && product.sellPrice) {
                                const profitPerUnit = product.sellPrice - product.buyPrice;
                                const totalProfitForItem = profitPerUnit * item.quantity;
                                return sum + totalProfitForItem;
                              }
                              return sum;
                            }, 0);
                            const discountAmount = totalProfitMargin * (customer.discountPercentage || 0) / 100;
                            const tax = total * (storeSettings.taxRate / 100);
                            const finalTotal = total + tax - discountAmount;
                            setSalesInvoice({
                              ...salesInvoice,
                              customerId: customerId,
                              customerName: customer.name,
                              discount: discountAmount,
                              total,
                              tax,
                              finalTotal
                            });
                          }
                        }
                      }}
                    >
                      <option value="GUEST">{t('walkInCustomer', 'زبون عابر')}</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name} - {customer.phone}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Payment Method */}
                  <div className="form-group-modern">
                    <label className="form-label">{t('paymentMethod', 'طريقة الدفع')}</label>
                    <select
                      className="form-select-modern"
                      value={salesInvoice.paymentMethod}
                      onChange={(e) => setSalesInvoice({...salesInvoice, paymentMethod: e.target.value})}
                    >
                      <option value="نقداً">{t('cash', 'نقداً')}</option>
                      <option value="دين">{t('credit', 'دين')}</option>
                    </select>
                  </div>

                  {/* Totals Summary */}
                  <div className="totals-summary-modern">
                    <div className="summary-header">
                      <h4>{t('finalTotal', 'المجموع النهائي')}</h4>
                    </div>

                    <div className="summary-lines">
                      <div className="summary-line">
                        <span className="label">{t('subtotal', 'المجموع الفرعي')}:</span>
                        <span className="value">{formatPrice(salesInvoice.total)}</span>
                      </div>

                      <div className="summary-line">
                        <span className="label">{t('tax', 'الضريبة')} ({storeSettings.taxRate}%):</span>
                        <span className="value">{formatPrice(salesInvoice.tax)}</span>
                      </div>

                      <div className="summary-line">
                        <span className="label">{t('discount', 'الخصم')}:</span>
                        <span className="value discount">{formatPrice(salesInvoice.discount)}</span>
                      </div>

                      <div className="summary-line final-total-line">
                        <span className="label">{t('finalTotal', 'المجموع النهائي')}:</span>
                        <span className="value final-total">{formatPrice(salesInvoice.finalTotal)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>



            {/* Action Buttons */}
            <div className="rapid-actions">
              <button
                className="save-btn"
                onClick={saveSalesInvoice}
                disabled={salesInvoice.items.length === 0}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 20V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16L21 8V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 21 19 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M17 21V13H7V21M7 3V8H15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {t('saveInvoice', 'حفظ الفاتورة')}
              </button>

              {/* Additional Action Buttons */}
              <button
                className="action-btn-1"
                onClick={() => {
                  // Add your custom action here
                  console.log('Action Button 1 clicked');
                  showToast('🔧 Action 1 executed', 'info', 2000);
                }}
                disabled={salesInvoice.items.length === 0}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L2 7V17L12 22L22 17V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 22V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M2 7L12 12L22 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {t('customAction1', 'إجراء مخصص 1')}
              </button>

              <button
                className="action-btn-2"
                onClick={() => {
                  // Add your custom action here
                  console.log('Action Button 2 clicked');
                  showToast('⚡ Action 2 executed', 'info', 2000);
                }}
                disabled={salesInvoice.items.length === 0}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {t('customAction2', 'إجراء مخصص 2')}
              </button>

              <button
                className="cancel-btn"
                onClick={() => setShowSalesModal(false)}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {t('cancel', 'إلغاء')}
              </button>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}
